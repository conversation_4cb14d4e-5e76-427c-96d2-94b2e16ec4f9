<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;
use App\Model\Behavior\AuditTrailBehavior;

/**
 * Banners Model
 *
 * @method \App\Model\Entity\Banner newEmptyEntity()
 * @method \App\Model\Entity\Banner newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Banner> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Banner get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Banner findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Banner patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Banner> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Banner|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Banner saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Banner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banner>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Banner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banner> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Banner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banner>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Banner>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Banner> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class BannersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->addBehavior('App.AuditTrail');

        $this->setTable('banners');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('title')
            ->maxLength('title', 255)
            ->requirePresence('title', 'create')
            ->notEmptyString('title');

        $validator
            ->scalar('title_ar')
            ->maxLength('title_ar', 255)
            ->requirePresence('title_ar', 'create')
            ->notEmptyString('title_ar');

        $validator
            ->scalar('summary')
            ->maxLength('summary', 255)
            ->allowEmptyString('summary');

        $validator
            ->scalar('summary_ar')
            ->maxLength('summary_ar', 255)
            ->allowEmptyString('summary_ar');

        $validator
            ->scalar('banner_location')
            ->notEmptyString('banner_location');

        $validator
            ->scalar('banner_type')
            ->requirePresence('banner_type', 'create')
            ->notEmptyString('banner_type');

        $validator
            ->scalar('url_link')
            ->maxLength('url_link', 255)
            ->allowEmptyString('url_link');

        $validator
            ->scalar('target_mob')
            ->allowEmptyString('target_mob');

        $validator
            ->boolean('display_in_web')
            ->allowEmptyString('display_in_web');

        $validator
            ->boolean('display_in_mobile')
            ->allowEmptyString('display_in_mobile');

        $validator
            ->scalar('web_banner')
            ->maxLength('web_banner', 255)
            ->allowEmptyString('web_banner');

        $validator
            ->scalar('mobile_banner')
            ->maxLength('mobile_banner', 255)
            ->allowEmptyString('mobile_banner');

        $validator
            ->date('start_date')
            ->allowEmptyDate('start_date');

        $validator
            ->date('end_date')
            ->allowEmptyDate('end_date');

        $validator
            ->notEmptyString('display_order');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    //S API/website : $type : web, mobile
    public function homeBanners($type)
    {

        $order = ['Banners.display_order' => 'ASC'];
        if ($type == 'mobile') {
            $select_col = ['id', 'title', 'summary', 'banner_type', 'url_link', 'mobile_banner', 'banner_location','target_mob'];
            $condition  = [
                'Banners.status' => 'A',
                'OR' => [
                    ['Banners.start_date <=' => date('Y-m-d'), 'Banners.end_date >=' => date('Y-m-d')],
                    ['Banners.start_date <=' => date('Y-m-d'), 'Banners.end_date IS' => null],
                    ['Banners.start_date IS' => null, 'Banners.end_date >=' => date('Y-m-d')],
                    ['Banners.start_date IS' => null, 'Banners.end_date IS' => null]
                ],
                'Banners.display_in_mobile' => 1
            ];
        } else {
            $select_col = ['id', 'title', 'summary', 'banner_type', 'url_link', 'web_banner', 'banner_location','target_mob'];
            $condition = [
                'Banners.status' => 'A',
                'OR' => [
                    ['Banners.start_date <=' => date('Y-m-d'), 'Banners.end_date >=' => date('Y-m-d')],
                    ['Banners.start_date <=' => date('Y-m-d'), 'Banners.end_date IS' => null],
                    ['Banners.start_date IS' => null, 'Banners.end_date >=' => date('Y-m-d')],
                    ['Banners.start_date IS' => null, 'Banners.end_date IS' => null]
                ],
                'Banners.display_in_web' => 1
            ];
        }

        $banners = $this->find()
            ->select($select_col)
            ->where($condition)
            ->order($order)
            ->disableHydration()
            ->toArray();

        return $banners;
    }

    public function delete(EntityInterface $banner, array $options = []): bool
    {
        $banner->status = 'D';
        if ($this->save($banner)) {
            return true;
        }
        return false;
    }
}
