# Region-Based Filtering Frontend Template Changes

This document outlines the frontend template changes needed to display country information in the admin panel for Brand, Category, Banners, CMS pages, and Widgets modules.

## Overview

The country filtering system is already implemented in the header (`templates/element/header.php`) with a dropdown that allows users to filter by country. The backend controllers now apply region-based filtering, and the frontend templates need to be updated to display country information.

## Template Changes Required

### 1. Categories Index Template (`templates/Categories/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($category->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($category->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

### 2. Banners Index Template (`templates/Banners/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($banner->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($banner->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

### 3. Content Pages Index Template (`templates/ContentPages/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($content_page->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($content_page->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

### 4. Widgets Index Template (`templates/Widgets/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($widget->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($widget->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

## Country Filter Header

The country filter dropdown is already implemented in `templates/element/header.php` and includes:

- **All Countries** option (shows all records regardless of country)
- **Individual Country** options (filters records by specific country)
- **Visual indicators** with icons and badges
- **Session persistence** (selection is saved and maintained across pages)

## CSS Classes Used

- `badge badge-success` - Green badge for specific countries
- `badge badge-secondary` - Gray badge for global/no country assigned
- `fas fa-map-marker-alt` - Icon for specific countries
- `fas fa-globe` - Icon for global/all countries

## JavaScript Integration

The country filtering works automatically through the existing JavaScript in the header that:

1. Handles dropdown selection
2. Sends AJAX requests to update session
3. Refreshes the page to apply filters
4. Updates the dropdown display text

## Implementation Notes

1. **Country Association**: All models now have `belongsTo('Countries')` relationship
2. **Controller Filtering**: All controllers use `applyRoleBasedCountryFilter()` method
3. **Permission Checks**: Edit/delete actions check `canUserAccessCountry()` before allowing access
4. **Super Admin Protection**: Super admin roles and users cannot be deleted/modified
5. **Database Migration**: Run the migration to add `country_id` fields to all tables

## Next Steps

1. Run the database migration: `bin/cake migrations migrate`
2. Update the frontend templates as shown above
3. Test the filtering functionality with different user roles
4. Verify permission restrictions work correctly
