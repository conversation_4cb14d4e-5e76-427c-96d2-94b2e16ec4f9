<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;
use Cake\ORM\Behavior\TreeBehavior;

/**
 * Categories Controller
 *
 * @property \App\Model\Table\CategoriesTable $Categories
 */
class CategoriesController extends AppController
{


    protected $Attributes;
    protected $AttributeValues;
    protected $CategoryAttributes;
    protected $ProductCategories;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Attributes = $this->fetchTable('Attributes');
        $this->AttributeValues = $this->fetchTable('AttributeValues');
        $this->CategoryAttributes = $this->fetchTable('CategoryAttributes');
        $this->ProductCategories = $this->fetchTable('ProductCategories');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        $query = $this->Categories
            ->find('threaded')
            ->select(['id', 'name', 'name_ar', 'parent_id', 'status', 'country_id']) // Added name_ar and country_id
            ->contain([
                'ParentCategories' => function ($q) {
                    return $q->select(['id', 'name', 'name_ar']); // Added name_ar
                },
                'ChildCategories' => function ($q) {
                    return $q->select(['id', 'name', 'name_ar', 'parent_id', 'status', 'country_id']) // Added name_ar and country_id
                        ->contain([
                            'ParentCategories' => function ($q) {
                                return $q->select(['id', 'name', 'name_ar']); // Added name_ar
                            },
                            'ChildCategories' => function ($q) {
                                return $q->select(['id', 'name', 'name_ar', 'parent_id', 'status', 'country_id']) // Added name_ar and country_id
                                    ->contain([
                                        'ParentCategories' => function ($q) {
                                            return $q->select(['id', 'name', 'name_ar']); // Added name_ar
                                        }
                                    ]);
                            }
                        ]);
                },
                'Countries' // Add Countries association to show country name
            ])->order(['Categories.name' => 'ASC']);

        $query->where(['Categories.status !=' => 'D']);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'Categories.country_id');

        $categories = $query->toArray();

        $title = 'Manage Categories';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        // $categories = $this->Categories->getCategoriesWithHierarchy();
        $this->set(compact('categories', 'title', 'status', 'statusMap'));
    }

    public function view($id = null)
    {
        $category = $this->Categories->get($id, contain: ['ParentCategories', 'BannerAds', 'BrandCategoryMappings', 'ChildCategories', 'CategoryAttributes', 'ProductCategories', 'WidgetCategoryMappings']);

        $category_icon = $this->Media->getCloudFrontURL($category->category_icon);
        $web_banner = $this->Media->getCloudFrontURL($category->web_banner);
        $mobile_banner = $this->Media->getCloudFrontURL($category->mobile_banner);

        // Only fetch active category attributes
        $categoryAttributes = $this->CategoryAttributes->find()
            ->contain([
                'Attributes' => [
                    'AttributeValues' => function ($q) {
                        return $q->where(['AttributeValues.status' => 'A']);
                    }
                ]
            ])
            ->where([
                'CategoryAttributes.category_id' => $id,
                'CategoryAttributes.status' => 'A' // Only get active attributes
            ])
            ->order(['CategoryAttributes.position' => 'ASC']) // Order by position
            ->all();

        $attributeList = [];
        foreach ($categoryAttributes as $categoryAttribute) {
            $attribute = $categoryAttribute->attribute;

            $attributeList[] = [
                'attribute_id' => $attribute->id,
                'attribute_name' => $attribute->name,
                'attribute_name_ar' => $attribute->name_ar,
                'attribute_values' => $attribute->attribute_values ?
                    collection($attribute->attribute_values)->extract('value')->toList() : [],
                'attribute_values_ar' => $attribute->attribute_values ?
                    collection($attribute->attribute_values)->extract('value_ar')->toList() : []
            ];
        }

        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');

        $title = 'Category | View';
        $this->set(compact('category', 'category_icon', 'web_banner', 'mobile_banner', 'title', 'status', 'statusMap', 'attributeList'));
    }

    public function add()
    {

        $this->set([
            'iconSize' => Configure::read('Constants.CATEGORY_ICON_SIZE'),
            'webBannerSize' => Configure::read('Constants.CATEGORY_WEB_BANNER_SIZE'),
            'iconType' => Configure::read('Constants.CATEGORY_ICON_JS_TYPE'),
            'webBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_JS_TYPE'),
            'iconMinWidth' => Configure::read('Constants.CATEGORY_ICON_MIN_WIDTH'),
            'iconMaxWidth' => Configure::read('Constants.CATEGORY_ICON_MAX_WIDTH'),
            'iconMinHeight' => Configure::read('Constants.CATEGORY_ICON_MIN_HEIGHT'),
            'iconMaxHeight' => Configure::read('Constants.CATEGORY_ICON_MAX_HEIGHT'),
            'webBannerMinWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_WIDTH'),
            'webBannerMaxWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_WIDTH'),
            'webBannerMinHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_HEIGHT'),
            'webBannerMaxHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_HEIGHT'),
            'categoryIconType' => Configure::read('Constants.CATEGORY_ICON_TYPE'),
            'categoryWebBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_TYPE'),
            'categoryMobileBannerType' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_TYPE'),
        ]);

        $category = $this->Categories->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $attribute_name_id = isset($data['attribute_name_id']) ? $data['attribute_name_id'] : '';
            $attribute_source = isset($data['attribute_source']) ? $data['attribute_source'] : '';
            $attribute_names = isset($data['attribute_name']) ? $data['attribute_name'] : '';
            $attribute_names_ar = isset($data['attribute_name_ar']) ? $data['attribute_name_ar'] : '';
            $attribute_values = isset($data['attribute_value']) ? $data['attribute_value'] : '';
            $attribute_values_ar = isset($data['attribute_value_ar']) ? $data['attribute_value_ar'] : '';
            $attribute_positions = isset($data['position']) ? $data['position'] : '';
            $attribute_position_values = isset($data['attribute_position']) ? $data['attribute_position'] : [];

            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Categories');
            if (isset($data['category_icon']) && $data['category_icon']->getError() === UPLOAD_ERR_OK) {


                $category_icon = $data['category_icon'];
                $fileName = trim($category_icon->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $category_icon->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Category Icon could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['category_icon'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['category_icon'] = '';
            }

            if (isset($data['web_banner']) && $data['web_banner']->getError() === UPLOAD_ERR_OK) {
                $web_banner = $data['web_banner'];
                $fileName = trim($web_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $web_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Web banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['web_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['web_banner'] = '';
            }

            // Make sure Arabic fields are set correctly
            // No need to do anything special as the form fields are already named correctly

            // Handle country assignment based on user's country filter selection
            $data = $this->handleCountryAssignment($data);

            $categorydata = $this->Categories->patchEntity($category, $data);
            $categorysave = $this->Categories->save($categorydata);
            if ($categorysave) {

                $catId = $categorysave->id;

                // Track which attributes we've processed
                $processedAttributeIds = [];

                // Process attributes from the form (both existing and new)
                $attributeCount = 0;
                if (is_array($attribute_names)) {
                    $attributeCount = count($attribute_names);
                    // Log attribute data for debugging
                    $this->log('Processing ' . $attributeCount . ' attributes', 'debug');
                    $this->log('Attribute names: ' . json_encode($attribute_names), 'debug');
                    $this->log('Attribute name IDs: ' . json_encode($attribute_name_id), 'debug');
                    $this->log('Attribute sources: ' . json_encode($attribute_source), 'debug');
                }

                for ($index = 0; $index < $attributeCount; $index++) {
                    // Skip empty rows
                    if (empty($attribute_names[$index]) && empty($attribute_name_id[$index])) {
                        continue;
                    }

                    // Get position from either the position input or the hidden attribute_position field
                    $position = isset($attribute_positions[$index]) ? $attribute_positions[$index] :
                               (isset($attribute_position_values[$index]) ? $attribute_position_values[$index] : $index);

                    // Case 1: Existing attribute selected from datalist
                    if (!empty($attribute_name_id[$index])) {
                        $attribute_id = $attribute_name_id[$index];
                        $processedAttributeIds[] = $attribute_id;

                        // Create new association
                        $categoryAttribute = $this->CategoryAttributes->newEntity([
                            'attribute_id' => $attribute_id,
                            'category_id' => $catId,
                            'position' => $position,
                            'status' => 'A'
                        ]);
                        $this->CategoryAttributes->save($categoryAttribute);

                        // Update the attribute with Arabic name if provided
                        if (isset($attribute_names_ar[$index]) && !empty($attribute_names_ar[$index])) {
                            $attribute = $this->Attributes->get($attribute_id);
                            $attribute->name_ar = $attribute_names_ar[$index];
                            $this->Attributes->save($attribute);
                        }

                        // Update attribute values
                        if (isset($attribute_values[$index]) && !empty($attribute_values[$index])) {
                            $values = array_map('trim', explode(',', $attribute_values[$index]));
                            $values_ar = isset($attribute_values_ar[$index]) ? array_map('trim', explode(',', $attribute_values_ar[$index])) : [];

                            // Then add/update values
                            for ($i = 0; $i < count($values); $i++) {
                                $value = $values[$i];
                                if (empty($value)) continue;

                                $value_ar = isset($values_ar[$i]) ? $values_ar[$i] : $value;

                                // Check if this value already exists
                                $existingValue = $this->AttributeValues->find()
                                    ->where([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value
                                    ])
                                    ->first();

                                if ($existingValue) {
                                    // Update existing value
                                    $existingValue->value_ar = $value_ar;
                                    $existingValue->status = 'A';
                                    $this->AttributeValues->save($existingValue);
                                } else {
                                    // Create new value
                                    $attributeValue = $this->AttributeValues->newEntity([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value,
                                        'value_ar' => $value_ar,
                                        'status' => 'A'
                                    ]);
                                    $this->AttributeValues->save($attributeValue);
                                }
                            }
                        }
                    }
                    // Case 2: New attribute entered manually
                    else if (!empty($attribute_names[$index])) {
                        $name = $attribute_names[$index];
                        $name_ar = isset($attribute_names_ar[$index]) ? $attribute_names_ar[$index] : $name;

                        // Log for debugging
                        $this->log('Processing new attribute: ' . $name, 'debug');
                        $this->log('Attribute source: ' . (isset($attribute_source[$index]) ? $attribute_source[$index] : 'not set'), 'debug');

                        // Force attribute source to 'manual' if not set or empty
                        if (!isset($attribute_source[$index]) || empty($attribute_source[$index])) {
                            $attribute_source[$index] = 'manual';
                            $this->log('Attribute source forced to manual', 'debug');
                        }

                        // Check if an attribute with this name already exists
                        $existingAttribute = $this->Attributes->find()
                            ->where([
                                'name' => $name,
                                'key_name' => 'category',
                                'status' => 'A'
                            ])
                            ->first();

                        if ($existingAttribute) {
                            $attribute_id = $existingAttribute->id;
                            $processedAttributeIds[] = $attribute_id;

                            // Update Arabic name
                            $existingAttribute->name_ar = $name_ar;
                            $this->Attributes->save($existingAttribute);

                            // Create new association
                            $categoryAttribute = $this->CategoryAttributes->newEntity([
                                'attribute_id' => $attribute_id,
                                'category_id' => $catId,
                                'position' => $position,
                                'status' => 'A'
                            ]);
                            $this->CategoryAttributes->save($categoryAttribute);
                        } else {
                            // Create new attribute
                            $attributeData = $this->Attributes->newEntity([
                                'name' => $name,
                                'name_ar' => $name_ar,
                                'key_name' => 'category',
                                'status' => 'A'
                            ]);

                            $attribute = $this->Attributes->save($attributeData);

                            if ($attribute) {
                                $attribute_id = $attribute->id;
                                $processedAttributeIds[] = $attribute_id;

                                // Create new association
                                $categoryAttribute = $this->CategoryAttributes->newEntity([
                                    'attribute_id' => $attribute_id,
                                    'category_id' => $catId,
                                    'position' => $position,
                                    'status' => 'A'
                                ]);
                                $this->CategoryAttributes->save($categoryAttribute);
                            } else {
                                throw new \Exception('Failed to save attribute');
                            }
                        }

                        // Process attribute values
                        if (isset($attribute_values[$index]) && !empty($attribute_values[$index])) {
                            $values = array_map('trim', explode(',', $attribute_values[$index]));
                            $values_ar = isset($attribute_values_ar[$index]) ? array_map('trim', explode(',', $attribute_values_ar[$index])) : [];

                            for ($i = 0; $i < count($values); $i++) {
                                $value = $values[$i];
                                if (empty($value)) continue;

                                $value_ar = isset($values_ar[$i]) ? $values_ar[$i] : $value;

                                // Check if this value already exists
                                $existingValue = $this->AttributeValues->find()
                                    ->where([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value
                                    ])
                                    ->first();

                                if ($existingValue) {
                                    // Update existing value
                                    $existingValue->value_ar = $value_ar;
                                    $existingValue->status = 'A';
                                    $this->AttributeValues->save($existingValue);
                                } else {
                                    // Create new value
                                    $attributeValue = $this->AttributeValues->newEntity([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value,
                                        'value_ar' => $value_ar,
                                        'status' => 'A'
                                    ]);
                                    $this->AttributeValues->save($attributeValue);
                                }
                            }
                        }
                    }
                }

                $this->Flash->success(__('The category has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The category could not be saved. Please, try again.'));
        }

        $parentCategories = $this->Categories->ParentCategories->find('list')->all();

        $categories = $this->Categories->find('treeList', keyPath: 'id', valuePath: 'name')
            ->where(['status' => 'A'])
            ->toArray();

        // $categories = $this->Categories->find('all')->where(['status' => 'A'])
        //     ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
        //     ->toArray();

        // $formattedCategories = $this->formatCategories($categories);

        $attributes = $this->Attributes->find('all')
            ->where(['key_name' => 'category'])
            ->toArray();

        // Get selected country for display
        $selectedCountryId = $this->request->getSession()->read('Admin.selectedCountryId');
        $selectedCountry = null;
        if ($selectedCountryId) {
            $selectedCountry = $this->Countries->getCountryById($selectedCountryId);
        }

        $title = 'Category | Add';
        $this->set(compact('category', 'parentCategories', 'categories', 'attributes', 'title', 'selectedCountry'));
    }

    function isArrayIndexEmpty($array)
    {
        return !isset($array[0]) || trim($array[0]) === '';
    }
    public function edit($id = null)
    {
        $this->set([
            'iconSize' => Configure::read('Constants.CATEGORY_ICON_SIZE'),
            'webBannerSize' => Configure::read('Constants.CATEGORY_WEB_BANNER_SIZE'),
            'iconType' => Configure::read('Constants.CATEGORY_ICON_JS_TYPE'),
            'webBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_JS_TYPE'),
            'iconMinWidth' => Configure::read('Constants.CATEGORY_ICON_MIN_WIDTH'),
            'iconMaxWidth' => Configure::read('Constants.CATEGORY_ICON_MAX_WIDTH'),
            'iconMinHeight' => Configure::read('Constants.CATEGORY_ICON_MIN_HEIGHT'),
            'iconMaxHeight' => Configure::read('Constants.CATEGORY_ICON_MAX_HEIGHT'),
            'webBannerMinWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_WIDTH'),
            'webBannerMaxWidth' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_WIDTH'),
            'webBannerMinHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MIN_HEIGHT'),
            'webBannerMaxHeight' => Configure::read('Constants.CATEGORY_WEB_BANNER_MAX_HEIGHT'),
            'categoryIconType' => Configure::read('Constants.CATEGORY_ICON_TYPE'),
            'categoryWebBannerType' => Configure::read('Constants.CATEGORY_WEB_BANNER_TYPE'),
            'categoryMobileBannerType' => Configure::read('Constants.CATEGORY_MOBILE_BANNER_TYPE'),
        ]);

        $category = $this->Categories->get($id, contain: [
            'CategoryAttributes' => function ($q) {
                return $q->where(['CategoryAttributes.status !=' => 'D'])
                         ->order(['CategoryAttributes.position' => 'ASC']);
            },
            'CategoryAttributes.Attributes.AttributeValues',
            'Countries'
        ]);

        // Check if user can access this category's country
        if (!$this->canUserAccessCountry($category->country_id)) {
            $this->Flash->error(__('You do not have permission to access this category.'));
            return $this->redirect(['action' => 'index']);
        }

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $attribute_name_id = isset($data['attribute_name_id']) ? $data['attribute_name_id'] : '';
            $attribute_source = isset($data['attribute_source']) ? $data['attribute_source'] : '';
            $attribute_names = isset($data['attribute_name']) ? $data['attribute_name'] : '';
            $attribute_names_ar = isset($data['attribute_name_ar']) ? $data['attribute_name_ar'] : '';
            $attribute_values = isset($data['attribute_value']) ? $data['attribute_value'] : '';
            $attribute_values_ar = isset($data['attribute_value_ar']) ? $data['attribute_value_ar'] : '';
            $attribute_positions = isset($data['position']) ? $data['position'] : '';
            $attribute_position_values = isset($data['attribute_position']) ? $data['attribute_position'] : [];


            $data['url_key'] = $this->generateUniqueUrlKey($data['name'], 'Categories', $id);

            if (isset($data['category_icon']) && $data['category_icon']->getError() === UPLOAD_ERR_OK) {
                $category_icon = $data['category_icon'];
                $fileName = trim($category_icon->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $category_icon->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Category Icon could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['category_icon'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['category_icon'] = $category->category_icon;
            }

            if (isset($data['web_banner']) && $data['web_banner']->getError() === UPLOAD_ERR_OK) {
                $web_banner = $data['web_banner'];
                $fileName = trim($web_banner->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $web_banner->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CATEGORY');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Web banner could not be uploaded. Please, try again.'));
                        return $this->redirect(['action' => 'add']);
                    } else {
                        $data['web_banner'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['web_banner'] = $category->web_banner;
            }

            // Make sure Arabic fields are set correctly
            // No need to do anything special as the form fields are already named correctly

            $categorydata = $this->Categories->patchEntity($category, $data);
            $categorysave = $this->Categories->save($categorydata);
            if ($categorysave) {

                $catId = $categorysave->id;

                // First, get all existing category attributes
                // We'll update the ones that are still present in the form
                // and mark the rest as deleted at the end
                $existingCategoryAttributes = $this->CategoryAttributes->find()
                    ->where(['category_id' => $catId, 'status !=' => 'D'])
                    ->all()
                    ->toArray();

                // Track which attributes we've processed
                $processedAttributeIds = [];

                // Process attributes from the form (both existing and new)
                $attributeCount = 0;
                if (is_array($attribute_names)) {
                    $attributeCount = count($attribute_names);
                    // Log attribute data for debugging
                    $this->log('Edit: Processing ' . $attributeCount . ' attributes', 'debug');
                    $this->log('Edit: Attribute names: ' . json_encode($attribute_names), 'debug');
                    $this->log('Edit: Attribute name IDs: ' . json_encode($attribute_name_id), 'debug');
                    $this->log('Edit: Attribute sources: ' . json_encode($attribute_source), 'debug');
                }

                for ($index = 0; $index < $attributeCount; $index++) {
                    // Skip empty rows
                    if (empty($attribute_names[$index]) && empty($attribute_name_id[$index])) {
                        continue;
                    }

                    // Get position from either the position input or the hidden attribute_position field
                    $position = isset($attribute_positions[$index]) ? $attribute_positions[$index] :
                               (isset($attribute_position_values[$index]) ? $attribute_position_values[$index] : $index);

                    // Case 1: Existing attribute selected from datalist
                    if (!empty($attribute_name_id[$index])) {
                        $attribute_id = $attribute_name_id[$index];
                        $processedAttributeIds[] = $attribute_id;

                        // Find if this attribute is already associated with the category
                        $existingCategoryAttribute = $this->CategoryAttributes->find()
                            ->where([
                                'category_id' => $catId,
                                'attribute_id' => $attribute_id,
                                'status !=' => 'D'
                            ])
                            ->first();

                        if ($existingCategoryAttribute) {
                            // Update existing association
                            $existingCategoryAttribute->position = $position;
                            $existingCategoryAttribute->status = 'A'; // Mark as active again
                            $this->CategoryAttributes->save($existingCategoryAttribute);
                        } else {
                            // Create new association
                            $categoryAttribute = $this->CategoryAttributes->newEntity([
                                'attribute_id' => $attribute_id,
                                'category_id' => $catId,
                                'position' => $position,
                                'status' => 'A'
                            ]);
                            $this->CategoryAttributes->save($categoryAttribute);
                        }

                        // Update the attribute with Arabic name if provided
                        if (isset($attribute_names_ar[$index]) && !empty($attribute_names_ar[$index])) {
                            $attribute = $this->Attributes->get($attribute_id);
                            $attribute->name_ar = $attribute_names_ar[$index];
                            $this->Attributes->save($attribute);
                        }

                        // Update attribute values
                        if (isset($attribute_values[$index]) && !empty($attribute_values[$index])) {
                            $values = array_map('trim', explode(',', $attribute_values[$index]));
                            $values_ar = isset($attribute_values_ar[$index]) ? array_map('trim', explode(',', $attribute_values_ar[$index])) : [];

                            // First, mark all existing values as deleted
                            $this->AttributeValues->updateAll(
                                ['status' => 'D'],
                                ['attribute_id' => $attribute_id, 'status' => 'A']
                            );

                            // Then add/update values
                            for ($i = 0; $i < count($values); $i++) {
                                $value = $values[$i];
                                if (empty($value)) continue;

                                $value_ar = isset($values_ar[$i]) ? $values_ar[$i] : $value;

                                // Check if this value already exists
                                $existingValue = $this->AttributeValues->find()
                                    ->where([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value
                                    ])
                                    ->first();

                                if ($existingValue) {
                                    // Update existing value
                                    $existingValue->value_ar = $value_ar;
                                    $existingValue->status = 'A'; // Mark as active again
                                    $this->AttributeValues->save($existingValue);
                                } else {
                                    // Create new value
                                    $attributeValue = $this->AttributeValues->newEntity([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value,
                                        'value_ar' => $value_ar,
                                        'status' => 'A'
                                    ]);
                                    $this->AttributeValues->save($attributeValue);
                                }
                            }
                        }
                    }
                    // Case 2: New attribute entered manually
                    else if (!empty($attribute_names[$index])) {
                        $name = $attribute_names[$index];
                        $name_ar = isset($attribute_names_ar[$index]) ? $attribute_names_ar[$index] : $name;

                        // Log for debugging
                        $this->log('Edit: Processing new attribute: ' . $name, 'debug');
                        $this->log('Edit: Attribute source: ' . (isset($attribute_source[$index]) ? $attribute_source[$index] : 'not set'), 'debug');

                        // Force attribute source to 'manual' if not set or empty
                        if (!isset($attribute_source[$index]) || empty($attribute_source[$index])) {
                            $attribute_source[$index] = 'manual';
                            $this->log('Edit: Attribute source forced to manual', 'debug');
                        }

                        // Check if an attribute with this name already exists
                        $existingAttribute = $this->Attributes->find()
                            ->where([
                                'name' => $name,
                                'key_name' => 'category',
                                'status' => 'A'
                            ])
                            ->first();

                        if ($existingAttribute) {
                            $attribute_id = $existingAttribute->id;
                            $processedAttributeIds[] = $attribute_id;

                            // Update Arabic name
                            $existingAttribute->name_ar = $name_ar;
                            $this->Attributes->save($existingAttribute);

                            // Find if this attribute is already associated with the category
                            $existingCategoryAttribute = $this->CategoryAttributes->find()
                                ->where([
                                    'category_id' => $catId,
                                    'attribute_id' => $attribute_id,
                                    'status !=' => 'D'
                                ])
                                ->first();

                            if ($existingCategoryAttribute) {
                                // Update existing association
                                $existingCategoryAttribute->position = $position;
                                $existingCategoryAttribute->status = 'A'; // Mark as active again
                                $this->CategoryAttributes->save($existingCategoryAttribute);
                            } else {
                                // Create new association
                                $categoryAttribute = $this->CategoryAttributes->newEntity([
                                    'attribute_id' => $attribute_id,
                                    'category_id' => $catId,
                                    'position' => $position,
                                    'status' => 'A'
                                ]);
                                $this->CategoryAttributes->save($categoryAttribute);
                            }
                        } else {
                            // Create new attribute
                            $attributeData = $this->Attributes->newEntity([
                                'name' => $name,
                                'name_ar' => $name_ar,
                                'key_name' => 'category',
                                'status' => 'A'
                            ]);

                            $attribute = $this->Attributes->save($attributeData);

                            if ($attribute) {
                                $attribute_id = $attribute->id;
                                $processedAttributeIds[] = $attribute_id;

                                // Create new association
                                $categoryAttribute = $this->CategoryAttributes->newEntity([
                                    'attribute_id' => $attribute_id,
                                    'category_id' => $catId,
                                    'position' => $position,
                                    'status' => 'A'
                                ]);
                                $this->CategoryAttributes->save($categoryAttribute);
                            } else {
                                throw new \Exception('Failed to save attribute');
                            }
                        }

                        // Process attribute values
                        if (isset($attribute_values[$index]) && !empty($attribute_values[$index])) {
                            $values = array_map('trim', explode(',', $attribute_values[$index]));
                            $values_ar = isset($attribute_values_ar[$index]) ? array_map('trim', explode(',', $attribute_values_ar[$index])) : [];

                            for ($i = 0; $i < count($values); $i++) {
                                $value = $values[$i];
                                if (empty($value)) continue;

                                $value_ar = isset($values_ar[$i]) ? $values_ar[$i] : $value;

                                // Check if this value already exists
                                $existingValue = $this->AttributeValues->find()
                                    ->where([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value
                                    ])
                                    ->first();

                                if ($existingValue) {
                                    // Update existing value
                                    $existingValue->value_ar = $value_ar;
                                    $existingValue->status = 'A'; // Mark as active again
                                    $this->AttributeValues->save($existingValue);
                                } else {
                                    // Create new value
                                    $attributeValue = $this->AttributeValues->newEntity([
                                        'attribute_id' => $attribute_id,
                                        'value' => $value,
                                        'value_ar' => $value_ar,
                                        'status' => 'A'
                                    ]);
                                    $this->AttributeValues->save($attributeValue);
                                }
                            }
                        }
                    }
                }

                // Mark any attributes that weren't processed as deleted
                foreach ($existingCategoryAttributes as $existingAttr) {
                    if (!in_array($existingAttr->attribute_id, $processedAttributeIds)) {
                        $existingAttr->status = 'D';
                        $this->CategoryAttributes->save($existingAttr);
                    }
                }

                $this->updateChildCategoriesStatus($category->id, $category->status);

                $this->Flash->success(__('The category has been updated.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The category could not be saved. Please, try again.'));
        }

        $aws_url = Configure::read('Settings.AWS_URL');

        $parentCategories = $this->Categories->ParentCategories->find('list')->all();

        $categories = $this->Categories->find('treeList', keyPath: 'id', valuePath: 'name')
            ->where(['status' => 'A'])
            ->toArray();

        $category_icon = $this->Media->getCloudFrontURL($category->category_icon);
        $web_banner = $this->Media->getCloudFrontURL($category->web_banner);
        $mobile_banner = $this->Media->getCloudFrontURL($category->mobile_banner);

        // echo "<pre>"; print_r($category); die;
        $attributes = $this->Attributes->find('all')
            ->where(['key_name' => 'category'])
            ->toArray();

        $title = 'Category | Edit';
        $this->set(compact('category', 'parentCategories', 'aws_url', 'categories', 'category_icon', 'web_banner', 'mobile_banner', 'attributes', 'title'));
    }

    private function updateChildCategoriesStatus($categoryId, $status)
    {



        $category = $this->Categories->get($categoryId);

        if ($status == 'D') {
            if ($category->brand_logo) {
                $this->Media->awsDelete($category->category_icon);
            }
            if ($category->web_banner) {
                $this->Media->awsDelete($category->web_banner);
            }
            if ($category->mobile_banner) {
                $this->Media->awsDelete($category->mobile_banner);
            }

            $category->category_icon = null;
            $category->web_banner = null;
            $category->mobile_banner = null;
        }

        $category->status = $status;
        $this->Categories->save($category);

        $childCategories = $this->Categories->find('all')
            ->where(['parent_id' => $categoryId])
            ->all();

        foreach ($childCategories as $child) {
            $child->status = $status;
            $this->Categories->save($child);
            $this->updateChildCategoriesStatus($child->id, $status);
        }
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The category could not be deleted. Please, try again.'];

        try {

            $activeProductsCount = $this->ProductCategories->find()
                ->matching('Products', function ($q) {
                    return $q->where(['Products.status' => 'A']);
                })
                ->where(['ProductCategories.category_id' => $id])
                ->count();

            if ($activeProductsCount > 0) {
                $response = ['success' => false, 'message' => "There are $activeProductsCount active products under this category. Please delete the products first."];
            } else {
                $this->updateChildCategoriesStatus($id, 'D');
                $response = ['success' => true, 'message' => 'The category has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function subcategories($categoryId = null)
    {

        $this->autoRender = false;

        $this->request->allowMethod(['get']);
        $subcategories = [];

        if ($categoryId) {

            $categories = $this->Categories
                ->find('children', ['for' => $categoryId])
                ->find('threaded')
                ->toArray();

            $dropdownData = $this->formatCategoriesForDropdown($categories);

            $result = ['status' => __('success'), 'data' => $dropdownData];
        } else {
            $result = ['status' => __('success'), 'data' => []];
        }

        $this->response->getBody()->write(json_encode($result));
        $this->response = $this->response->withType('json');
        return $this->response;
    }

    function formatCategoriesForDropdown($categories, $prefix = '')
    {
        $dropdown = [];

        foreach ($categories as $category) {
            $dropdown[$prefix . $category->name] = $category->id;

            if (!empty($category->children)) {
                $dropdown += $this->formatCategoriesForDropdown($category->children, $prefix . '-- ');
            }
        }

        return $dropdown;
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $categoryId = $this->request->getData('image_id');

        if (!$categoryId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $category = $this->Categories->get($categoryId);

        if (!$category) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Category not found']));
        }

        $existingImagePath = $category->category_icon;

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $category->category_icon = null;
            if ($this->Categories->save($category)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update category']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }

    /**
     * Delete Category Attribute method
     *
     * @return \Cake\Http\Response|null
     */
    public function deleteCategoryAttribute()
    {
        $this->request->allowMethod(['post']);

        $attributeId = $this->request->getData('attribute_id');
        $categoryId = $this->request->getData('category_id');

        if (!$attributeId || !$categoryId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $categoryAttribute = $this->CategoryAttributes->find()
            ->where([
                'attribute_id' => $attributeId,
                'category_id' => $categoryId,
                'status !=' => 'D'
            ])
            ->first();

        if (!$categoryAttribute) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Category attribute not found']));
        }

        // Set status to 'D' instead of deleting
        $categoryAttribute->status = 'D';

        if ($this->CategoryAttributes->save($categoryAttribute)) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Category attribute has been marked as deleted']));
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update category attribute']));
        }
    }
}
