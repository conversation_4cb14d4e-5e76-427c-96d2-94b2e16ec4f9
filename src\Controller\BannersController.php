<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;

/**
 * Banners Controller
 *
 * @property \App\Model\Table\BannersTable $Banners
 */
class BannersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
    }

    public function index()
    {
        $query = $this->Banners->find()
            ->contain(['Countries']) // Add Countries association to show country name
            ->where(['status !=' => 'D'])
            ->applyOptions(['order' => ['title' => 'ASC']]);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'Banners.country_id');

        $banners = $query->all();
        foreach ($banners as $banner) {
            if (!empty($banner->web_banner)) {
                $banner->web_media = $this->Media->getCloudFrontURL($banner->web_banner);
            }

            if (!empty($banner->mob_banner)) {
                $banner->mob_media = $this->Media->getCloudFrontURL($banner->mob_banner);
            }
        }
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        // Removed dateFormat and timeFormat since date columns were removed from the list

        $this->set(compact('banners', 'status', 'statusMap'));
    }

    /**
     * View method
     *
     * @param string|null $id Banner id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $banner = $this->Banners->get($id, contain: []);
        $status = Configure::read('Constants.STATUS');
        $bannerloc = Configure::read('Constants.BANNER_LOCATION');
        $web_media = $this->Media->getCloudFrontURL($banner->web_banner);

        // Removed variables for fields that were removed from view: bannerviewVal, taregtMob, mob_media, dateFormat, timeFormat, statusMap, bannertype
        $this->set(compact('banner', 'status', 'bannerloc', 'web_media'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $banner = $this->Banners->newEmptyEntity();
        $bannertype = Configure::read('Constants.BANNER_TYPE');
        $bannerloc = Configure::read('Constants.BANNER_LOCATION');
        $taregtMob = Configure::read('Constants.TARGET_MOB');
        $this->set([
            'webImageSize' => Configure::read('Constants.BANNER_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.BANNER_MOB_IMAGE_SIZE'),
            'webVideoSize' => Configure::read('Constants.BANNER_WEB_VIDEO_SIZE'),
            'mobVideoSize' => Configure::read('Constants.BANNER_MOB_VIDEO_SIZE'),
            'webImageTypedisp' => Configure::read('Constants.BANNER_WEB_IMAGE_TYPE_DISP'),
            'webImageType' => Configure::read('Constants.BANNER_WEB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.BANNER_MOB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.BANNER_MOB_IMAGE_JS_TYPE'),
            'webVideoType' => Configure::read('Constants.BANNER_WEB_VIDEO_JS_TYPE'),
            'webVideoTypedisp' => Configure::read('Constants.BANNER_WEB_VIDEO_TYPE_DISP'),
            'mobVideoType' => Configure::read('Constants.BANNER_MOB_VIDEO_JS_TYPE'),
            'mobVideoTypedisp' => Configure::read('Constants.BANNER_MOB_VIDEO_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        // Removed bannerview and taregtMob since those fields were removed from the form
        $this->set(compact('banner', 'bannertype', 'bannerloc'));
        if ($this->request->is('post')) {
            $bannerData = $this->request->getData();

            // Debug: Log the received data to check if title_ar and summary_ar are present
            $this->log('Banner Add Data: ' . json_encode($bannerData), 'debug');

            // Set default banner type to Image since we removed the selection
            $bannerData['banner_type'] = 'Image';

            // Only handle image uploads now
            $allowedFormats = Configure::read('Constants.BANNER_WEB_IMAGE_TYPE');
            $maxWebMediaSize = Configure::read('Constants.BANNER_WEB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($bannerData['web_media_file']) && $bannerData['web_media_file']->getError() === UPLOAD_ERR_OK) {
                $web_media = $bannerData['web_media_file'];
                $webMediaName = trim($web_media->getClientFilename());
                $webMediaSize = $web_media->getSize();
                $webMediaExt = strtolower(pathinfo($webMediaName, PATHINFO_EXTENSION));

                if (!in_array($webMediaExt, $allowedFormats)) {
                    $allowedFormatsStr = implode(', ', $allowedFormats);
                    return $this->Flash->error(__('Invalid file type for banner image. Only ' . $allowedFormatsStr . ' are allowed.'));
                }

                if ($webMediaSize > $maxWebMediaSize) {
                    $maxSizeMB = Configure::read('Constants.BANNER_WEB_IMAGE_SIZE');
                    return $this->Flash->error(__('Banner image size exceeds the maximum allowed size of ' . $maxSizeMB . ' MB.'));
                }

                // Remove dimension validation as requested - only suggestions now

                if (!empty($webMediaName)) {
                    $webMeidaTmpName = $web_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.BANNER_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webMediaFile = pathinfo($webMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webMediaExt;
                    $uploadResult = $this->Media->upload($webMeidaTmpName, $targetdir, $webMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['web_banner'] = $uploadFolder . $webMediaFile;
                    }
                }
            }


            // Set default values since banner_view field was removed
            $bannerData['display_in_web'] = 1; // Default to display on web
            $bannerData['display_in_mobile'] = 1; // Default to display on mobile
            // Remove target_mob since field was removed
            $bannerData['target_mob'] = null;
            $banner = $this->Banners->patchEntity($banner, $bannerData);
            if ($this->Banners->save($banner)) {
                $this->Flash->success(__('The banner has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The banner could not be saved. Please, try again.'));
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Banner id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $banner = $this->Banners->get($id, contain: []);
        $this->set([
            'webImageSize' => Configure::read('Constants.BANNER_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.BANNER_MOB_IMAGE_SIZE'),
            'webVideoSize' => Configure::read('Constants.BANNER_WEB_VIDEO_SIZE'),
            'mobVideoSize' => Configure::read('Constants.BANNER_MOB_VIDEO_SIZE'),
            'webImageType' => Configure::read('Constants.BANNER_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.BANNER_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.BANNER_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.BANNER_MOB_IMAGE_TYPE_DISP'),
            'webVideoType' => Configure::read('Constants.BANNER_WEB_VIDEO_JS_TYPE'),
            'webVideoTypedisp' => Configure::read('Constants.BANNER_WEB_VIDEO_TYPE_DISP'),
            'mobVideoType' => Configure::read('Constants.BANNER_MOB_VIDEO_JS_TYPE'),
            'mobVideoTypedisp' => Configure::read('Constants.BANNER_MOB_VIDEO_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.BANNER_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.BANNER_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $bannertype = Configure::read('Constants.BANNER_TYPE');
        $bannerloc = Configure::read('Constants.BANNER_LOCATION');
        $web_media = $this->Media->getCloudFrontURL($banner->web_banner);
        $mob_media = $this->Media->getCloudFrontURL($banner->mobile_banner);
        // Removed bannerview, bannerviewVal, and taregtMob since those fields were removed from the form
        $status = Configure::read('Constants.STATUS');
        $this->set(compact('banner', 'bannertype', 'bannerloc', 'web_media', 'mob_media', 'status'));
        if ($this->request->is(['patch', 'post', 'put'])) {
            $bannerData = $this->request->getData();

            // Debug: Log the received data to check if title_ar and summary_ar are present
            $this->log('Banner Edit Data: ' . json_encode($bannerData), 'debug');

            // Set default banner type to Image since we removed the selection
            $bannerData['banner_type'] = 'Image';

            // Only handle image uploads now
            $allowedFormats = Configure::read('Constants.BANNER_WEB_IMAGE_TYPE');
            $maxWebMediaSize = Configure::read('Constants.BANNER_WEB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($bannerData['web_media_file']) && $bannerData['web_media_file']->getError() === UPLOAD_ERR_OK) {
                $web_media = $bannerData['web_media_file'];
                $webMediaName = trim($web_media->getClientFilename());
                $webMediaSize = $web_media->getSize();
                $webMediaExt = strtolower(pathinfo($webMediaName, PATHINFO_EXTENSION));

                if (!in_array($webMediaExt, $allowedFormats)) {
                    $allowedFormatsStr = implode(', ', $allowedFormats);
                    return $this->Flash->error(__('Invalid file type for banner image. Only ' . $allowedFormatsStr . ' are allowed.'));
                }

                if ($webMediaSize > $maxWebMediaSize) {
                    $maxSizeMB = Configure::read('Constants.BANNER_WEB_IMAGE_SIZE');
                    return $this->Flash->error(__('Banner image size exceeds the maximum allowed size of ' . $maxSizeMB . ' MB.'));
                }

                // Remove dimension validation as requested - only suggestions now

                if (!empty($webMediaName)) {
                    $webMeidaTmpName = $web_media->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.BANNER_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webMediaFile = pathinfo($webMediaName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webMediaExt;
                    $uploadResult = $this->Media->upload($webMeidaTmpName, $targetdir, $webMediaFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $bannerData['web_banner'] = $uploadFolder . $webMediaFile;
                    }
                }
            }


            // Set default values since banner_view field was removed
            $bannerData['display_in_web'] = 1; // Default to display on web
            $bannerData['display_in_mobile'] = 1; // Default to display on mobile
            // Remove target_mob since field was removed
            $bannerData['target_mob'] = null;

            $banner = $this->Banners->patchEntity($banner, $bannerData);
            if ($this->Banners->save($banner)) {
                $this->Flash->success(__('The banner has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The banner could not be saved. Please, try again.'));
        }
    }

    /**
     * Delete method
     *
     * @param string|null $id Banner id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $banner = $this->Banners->get($id);
        $response = ['success' => false, 'message' => 'The banner could not be deleted. Please, try again.'];
        if ($banner) {
            if ($this->Banners->delete($banner)) {
                $response = ['success' => true, 'message' => 'The banner has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The banner could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The banner does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $bannerId = $this->request->getData('image_id');

        if (!$imageType || !$bannerId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $banner = $this->Banners->get($bannerId);

        if (!$banner) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Banner not found']));
        }

        $imageField = $imageType === 'web' ? 'web_banner' : 'mobile_banner';
        $existingImagePath = $banner->{$imageField};

        if ($existingImagePath) {
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');

            $filePath = WWW_ROOT . $uploadFolder . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $banner->{$imageField} = null;
            if ($this->Banners->save($banner)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update banner']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }
}
