<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Core\Configure;

/**
 * Widgets Controller
 *
 * @property \App\Model\Table\WidgetsTable $Widgets
 */
class WidgetsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\CategoriesTable $Categories;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Categories = $this->fetchTable('Categories');
    }

    public function index()
    {
        $query = $this->Widgets->find()
            ->contain(['Countries']) // Add Countries association to show country name
            ->where(['status !=' => 'D'])
            ->applyOptions(['order' => ['title' => 'ASC']]);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'Widgets.country_id');

        $widgets = $query->all();

        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $widgetTypes = Configure::read('Constants.WIDGET_TYPE');
        $this->set(compact('widgets', 'status', 'statusMap', 'widgetTypes'));
    }

    /**
     * View method
     *
     * @param string|null $id Widget id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {    
        $widget = $this->Widgets->find()
            ->contain([
                'WidgetCategoryMappings' => function ($q) {
                    return $q->where(['WidgetCategoryMappings.status !=' => 'D'])
                        ->contain(['Categories']);
                }
            ])
            ->where(['Widgets.id' => $id])
            ->firstOrFail();

        $selectedCatgeories = [];
        foreach ($widget->widget_category_mappings as $WidgetCategoryMapping) {
            $selectedCatgeories[] = $WidgetCategoryMapping->category->name;
        }
        $selectedCatgeories = implode(', ', $selectedCatgeories);
        $widgetTypes = Configure::read('Constants.WIDGET_TYPE');
        $productPreference = Configure::read('Constants.PRODUCT_PREFERENCE');
        $statuses = Configure::read('Constants.STATUS');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $web_image = $this->Media->getCloudFrontURL($widget->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($widget->mobile_image);
        $this->set(compact('widget', 'widgetTypes', 'productPreference', 'statuses', 'dateFormat', 'selectedCatgeories', 'web_image', 'mobile_image'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $widget = $this->Widgets->newEmptyEntity();
        $maxId = $this->Widgets->find()
            ->select(['max_id' => $this->Widgets->find()->func()->max('id')])
            ->first()
            ->max_id;

        $nextId = $maxId + 1;
        $categories = $this->Categories->find('all')->where(['status' => 'A'])
            ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
            ->toArray();

        $formattedCategories = $this->formatCategories($categories);

        $widgetTypes = Configure::read('Constants.WIDGET_TYPE');
        $productPreference = Configure::read('Constants.PRODUCT_PREFERENCE');
        $widgetCheckboxes = Configure::read('Constants.WIDGET_DISPLAY_CHECKBOXES');
        $this->set([
            'webImageSize' => Configure::read('Constants.WIDGET_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.WIDGET_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.WIDGET_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.WIDGET_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.WIDGET_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.WIDGET_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $this->set(compact('widget', 'widgetTypes', 'productPreference', 'formattedCategories', 'widgetCheckboxes', 'nextId'));

        if ($this->request->is('post')) {
            $widgetData = $this->request->getData();
            $allowedFormats = Configure::read('Constants.WIDGET_WEB_IMAGE_TYPE');
            $maxWebImageSize = Configure::read('Constants.WIDGET_WEB_IMAGE_SIZE') * 1024 * 1024;
            $maxMobileImageSize = Configure::read('Constants.WIDGET_MOB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($widgetData['web_image_file']) && $widgetData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                $web_image = $widgetData['web_image_file'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.WIDGET_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $widgetData['web_image'] = $uploadFolder . $webImageFile;
                    }
                }
            }

            if (!empty($widgetData['mobile_image_file']) && $widgetData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                $mob_image = $widgetData['mobile_image_file'];
                $mobImageName = trim($mob_image->getClientFilename());
                $mobImageSize = $mob_image->getSize();
                $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                if (!in_array($mobImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobImageSize > $maxMobileImageSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($mobImageName)) {
                    $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.WIDGET_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                    $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $widgetData['mobile_image'] = $uploadFolder . $mobImageFile;
                    }
                }
            }
            if (empty($widgetData['product_preference'])) {
                $widgetData['product_preference'] = null;
            }

            // Handle country assignment based on user's country filter selection
            $widgetData = $this->handleCountryAssignment($widgetData);

            $widget = $this->Widgets->patchEntity($widget, $widgetData);
            if ($this->Widgets->save($widget)) {

                $widgetId = $widget->id;
                $categoryIds = $this->request->getData('widget_category_mappings._ids');

                $this->saveWidgetCategories($widgetId, $categoryIds, $formattedCategories);

                $this->Flash->success(__('The widget has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The widget could not be saved. Please, try again.'));
        }
    }

    /**
     * Edit method
     *
     * @param string|null $id Widget id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
     //   echo $widget->web_image; die;

     
        $widget = $this->Widgets->find()
            ->contain([
                'WidgetCategoryMappings' => function ($q) {
                    return $q->where(['WidgetCategoryMappings.status !=' => 'D']);
                },
                'Countries'
            ])
            ->where(['Widgets.id' => $id])
            ->firstOrFail();

        // Check if user can access this widget's country
        if (!$this->canUserAccessCountry($widget->country_id)) {
            $this->Flash->error(__('You do not have permission to access this widget.'));
            return $this->redirect(['action' => 'index']);
        }

        $nextId = $widget['id'];
        // $categories = $this->Categories->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])->where(['status' => 'A'])->all()->toArray();

        // if (empty($categories)) {
        //     $categories = ['' => 'No categories available'];
        // }
        $widgetTypes = Configure::read('Constants.WIDGET_TYPE');
        $productPreference = Configure::read('Constants.PRODUCT_PREFERENCE');
        $widgetCheckboxes = Configure::read('Constants.WIDGET_DISPLAY_CHECKBOXES');
        $statuses = Configure::read('Constants.STATUS');
        $selectedCatgeories = array_column($widget->widget_category_mappings, 'category_id');
        $this->set([
            'webImageSize' => Configure::read('Constants.WIDGET_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.WIDGET_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.WIDGET_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.WIDGET_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.WIDGET_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.WIDGET_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $web_image = $this->Media->getCloudFrontURL($widget->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($widget->mobile_image);
        $categories = $this->Categories->find('all')->where(['status' => 'A'])
            ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
            ->toArray();
        $formattedCategories = $this->formatCategories($categories);

       
 
       
        $this->set(compact('widget', 'widgetTypes', 'productPreference', 'widgetCheckboxes', 'nextId', 'selectedCatgeories', 'statuses', 'web_image', 'mobile_image', 'formattedCategories'));

        if ($this->request->is(['patch', 'post', 'put'])) {
            $widgetData = $this->request->getData();
            $allowedFormats = Configure::read('Constants.WIDGET_WEB_IMAGE_TYPE');
            $maxWebImageSize = Configure::read('Constants.WIDGET_WEB_IMAGE_SIZE') * 1024 * 1024;
            $maxMobileImageSize = Configure::read('Constants.WIDGET_MOB_IMAGE_SIZE') * 1024 * 1024;

            if (!empty($widgetData['web_image_file']) && $widgetData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                $web_image = $widgetData['web_image_file'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.WIDGET_WEB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.WIDGET_WEB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.WIDGET_WEB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $widgetData['web_image'] = $uploadFolder . $webImageFile;
                    }
                }
            }

            if (!empty($widgetData['mobile_image_file']) && $widgetData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                $mob_image = $widgetData['mobile_image_file'];
                $mobImageName = trim($mob_image->getClientFilename());
                $mobImageSize = $mob_image->getSize();
                $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                if (!in_array($mobImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($mobImageSize > $maxMobileImageSize) {
                    return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_WIDTH');
                $minHeight = Configure::read('Constants.WIDGET_MOB_IMAGE_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.WIDGET_MOB_IMAGE_MAX_HEIGHT');

                if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                    return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                }

                if (!empty($mobImageName)) {
                    $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.WIDGET_MOB_IMAGE');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                    $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $widgetData['mobile_image'] = $uploadFolder . $mobImageFile;
                    }
                }
            }
            if (empty($widgetData['product_preference'])) {
                $widgetData['product_preference'] = null;
            }
            $widget = $this->Widgets->patchEntity($widget, $widgetData);
            if ($this->Widgets->save($widget)) {
                $widgetId = $id;
                $categoryIds = $this->request->getData('widget_category_mappings._ids');
                $this->saveWidgetCategories($widgetId, $categoryIds, $formattedCategories);
                $this->Flash->success(__('The widget has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The widget could not be saved. Please, try again.'));
        }
    }

    /**
     * Delete method
     *
     * @param string|null $id Widget id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $widget = $this->Widgets->get($id);
        $response = ['success' => false, 'message' => 'The widget could not be deleted. Please, try again.'];

        // Check if user can access this widget's country
        if (!$this->canUserAccessCountry($widget->country_id)) {
            $response = ['success' => false, 'message' => 'You do not have permission to delete this widget.'];
        } elseif ($widget) {
            if ($this->Widgets->delete($widget)) {
                $response = ['success' => true, 'message' => 'The widget has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The widget could not be delted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The widget does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    protected function saveWidgetCategories($widgetId, $categoryIds, $formattedCategories)
    {
        $widgetCategoriesMappingsTable = $this->Widgets->WidgetCategoryMappings;

        $widgetCategoriesMappingsTable->updateAll(
            ['status' => 'D'],
            ['widget_id' => $widgetId]
        );

        $categoryIds = is_array($categoryIds) ? array_filter($categoryIds) : [];

        if (!empty($categoryIds)) {

            $data = [];
            foreach ($categoryIds as $categoryId) {
                $categoryLevel = $formattedCategories[$categoryId]['level'] ?? 0;
                $data[] = [
                    'widget_id' => $widgetId,
                    'category_id' => $categoryId,
                    'level' => $categoryLevel,
                    'status' => 'A'
                ];
            }

            $entities = $widgetCategoriesMappingsTable->newEntities($data);
            if (!$widgetCategoriesMappingsTable->saveMany($entities)) {
                $this->Flash->error(__('The category associations could not be saved. Please, try again.'));
            }
        }
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $widgetId = $this->request->getData('image_id');

        if (!$imageType || !$widgetId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $widget = $this->Widgets->get($widgetId);

        if (!$widget) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Widget not found']));
        }

        if ($imageType === 'web') {
            $uploadFolder = Configure::read('Constants.WIDGET_WEB_IMAGE');
        } else if ($imageType === 'mobile') {
            $uploadFolder = Configure::read('Constants.WIDGET_MOB_IMAGE');
        }

        $imageField = $imageType === 'web' ? 'web_image' : 'mobile_image';
        $existingImagePath = $widget->{$imageField};

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $widget->{$imageField} = null;
            if ($this->Widgets->save($widget)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update Widget']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }

    protected function formatCategories($categories, $parentId = null, $level = 0)
    {
        $result = [];
        foreach ($categories as $category) {
            if ($category->parent_id == $parentId) {
                $result[$category->id] = [
                    'text' => str_repeat('--', $level) . ' ' . $category->name,
                    'level' => $level
                ];
                $result += $this->formatCategories($categories, $category->id, $level + 1);
            }
        }
        return $result;
    }
}
