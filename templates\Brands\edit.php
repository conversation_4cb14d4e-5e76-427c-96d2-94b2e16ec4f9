<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Brand $brand
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'index']) ?>"><?= __("Brands") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Edit") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #206331"><?= __("Edit Brand") ?></h6>
            <?php //echo "<pre>"; print_r($brand); die; ?>
            <?php echo $this->Form->create($brand, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>
            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Brand Name") ?> <sup class="text-danger font-11">*</sup>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => __('Enter Brand Name'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="name_ar" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Brand Name (Arabic)") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('name_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name_ar',
                        'placeholder' => __('Enter Brand Name in Arabic'),
                        'label' => false,
                        'dir' => 'rtl'
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Description") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description',
                        'placeholder' => __('Enter Description'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description_ar" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Description (Arabic)") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('description_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description_ar',
                        'placeholder' => __('Enter Description in Arabic'),
                        'label' => false,
                        'dir' => 'rtl'
                    ]); ?>
                </div>
            </div>

            <!-- Country Assignment Display -->
            <div class="form-group row">
                <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
                <div class="col-sm-5">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php if (!empty($brand->country)): ?>
                            <?= __('This brand is assigned to: <strong>{0}</strong>', h($brand->country->name)) ?>
                        <?php else: ?>
                            <?= __('This brand is assigned to: <strong>Global (All Countries)</strong>') ?>
                        <?php endif; ?>
                        <br>
                        <small class="text-muted">
                            <?= __('Country assignment was set when the brand was created.') ?>
                        </small>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="brand_logo" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Brand Logo") ?> <sup class="text-danger font-11">*</sup>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('brand_logo', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'brand_logo',
                        'title' => __('Upload Brand Logo'),
                        'label' => false,
                        'accept' => implode(',', $logoType)
                    ]); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $brandLogoType)) ?> files are allowed. Max size: <?php echo $logoSize; ?> MB. Dimensions: <?= $logoMinWidth ?> x <?= $logoMinHeight ?> and <?= $logoMaxWidth ?> x <?= $logoMaxHeight ?>.</small>
                    <?= $this->Form->hidden('existing_brand_logo', ['value' => !empty($brand->brand_logo) ? $brand->brand_logo : '', 'id' => 'existing_brand_logo']); ?>
                    <div id="logoPreviewContainer">
                        <?php if (!empty($brand->brand_logo)):
                            $imageName = !empty($brand->brand_logo) ? basename($brand->brand_logo) : '';
                            ?>
                            <div class="mt-3 logo-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                                <img src="<?= $brand_logo; ?>" alt="<?= __('Brand Logo') ?>"
                                    style="max-width: 100px; max-height: 100px;">
                                <span class="image-name" title="<?= $imageName ?>"><?= $imageName ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="web_banner" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Website Banner") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('web_banner', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'web_banner',
                        'title' => __('Upload Website Banner'),
                        'label' => false,
                        'accept' => implode(',', $bannerType)
                    ]); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $brandBannerType)) ?> files are allowed. Max size: <?php echo $bannerSize; ?> MB.</small>
                    <?= $this->Form->hidden('existing_web_banner', ['value' => !empty($brand->web_banner) ? $brand->web_banner : '', 'id' => 'existing_web_banner']); ?>
                    <div id="bannerPreviewContainer">
                        <?php if (!empty($brand->web_banner)):
                            $imageName = !empty($brand->web_banner) ? basename($brand->web_banner) : ''; ?>
                            <div class="mt-3 banner-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                                <img src="<?= $web_banner; ?>" alt="<?= __('Website Banner') ?>"
                                    style="max-width: 100px; max-height: 100px;">
                                <span class="image-name" title="<?= $imageName ?>"><?= $imageName ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="parent_id" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Category") ?> <sup class="text-danger font-11">*</sup>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('category_id', [
                        'id' => 'category_id_model',
                        'type' => 'select',
                        'label' => false,
                        'div' => false,
                        'title' => __('Select Category'),
                        'options' => $categories,
                        'multiple' => 'multiple',
                        'data-live-search' => "true",
                        'class' => 'form-control select2',
                        'value' => $selectedCategories
                    ]); ?>
                </div>
            </div>

            <h6 class="m-b-20" style="color: #206331"><?= __("SEO Configuration") ?></h6>

            <div class="form-group row">
                <label for="meta_title" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Meta Title") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('meta_title', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_title',
                        'placeholder' => __('Enter Meta Title'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="meta_keyword" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Meta Keyword") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('meta_keyword', [
                        'type' => 'text',
                        'class' => 'form-control inputtags',
                        'id' => 'meta_keyword',
                        'placeholder' => __('Enter Meta Keywords'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="meta_description" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Meta Description") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('meta_description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_description',
                        'placeholder' => __('Enter Meta Description'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold">
                    <?= __("Status") ?>
                </label>
                <div class="col-sm-5 main-field">
                    <?= $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => __('Active'),
                            'I' => __('Inactive')
                        ],
                        'empty' => __('Select Status'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn btn-primary"><?= __("Save") ?></button>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";

</script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>
<script>
    $(document).ready(function () {
        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });
    });

    $('#web_banner').on('change', async function() {

        let isValid = await asyncvalidateFile(
            this,
            <?= $bannerSize ?> * 1024 * 1024,
            <?= json_encode($bannerType) ?>
        );

        if (isValid && this.files && this.files[0]) {
            let file = this.files[0];
            let reader = new FileReader();

            reader.onload = function(e) {
                let base64Url = e.target.result;

                let imageHtml = `
                    <div class="mt-3 banner-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                        <img src="${base64Url}" alt="Web Banner" style="max-width: 100px; max-height: 100px;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                    </div>
                `;

                $('#bannerPreviewContainer').html(imageHtml);
            };

            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };

            reader.readAsDataURL(file);
        }
    });

    $('#brand_logo').on('change', async function() {

        let isValid = await asyncvalidateFile(
            this,
            <?= $logoSize ?> * 1024 * 1024,
            <?= json_encode($logoType) ?>
        );

        if (isValid && this.files && this.files[0]) {
            let file = this.files[0];
            let reader = new FileReader();

            reader.onload = function(e) {
                let base64Url = e.target.result;

                let imageHtml = `
                    <div class="mt-3 logo-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                        <img src="${base64Url}" alt="Brand Logo" style="max-width: 100px; max-height: 100px;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                    </div>
                `;

                $('#logoPreviewContainer').html(imageHtml);
            };

            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };

            reader.readAsDataURL(file);
        }
    });

    var validationMessages = {
        nameRequired: "<?= __('Please enter brand name') ?>",
        brandLogoRequired: "<?= __('Please add brand logo') ?>",
        categoryRequired: "<?= __('Please select category') ?>",
        patternError: "<?= __('Only lowercase letters and underscores are allowed') ?>"
    };

    $(document).ready(function () {
        $.validator.addMethod("pattern", function (value, element) {
            return this.optional(element) || /^[a-z-]+$/.test(value);
        }, validationMessages.patternError);

        $("#add").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                'brand_logo': {
                    required: function () {
                        var existingLogo = $('input[name="existing_brand_logo"]').val();
                        return !existingLogo;
                    }
                },
                'category_id[]': {
                    required: true
                }
            },
            messages: {
                'name': {
                    required: validationMessages.nameRequired
                },
                'brand_logo': {
                    required: validationMessages.brandLogoRequired
                },
                'category_id[]': {
                    required: validationMessages.categoryRequired
                }
            },
            submitHandler: function (form) {
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');
            }
        });
    });
</script>
<?php $this->end(); ?>